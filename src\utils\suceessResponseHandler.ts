import { Response } from 'express';

// Extend Response interface to include requestId


export const ResponseUtil = {
  success: (requestId: string,res: Response, message: string, result?: any) => {

    res.status(200).json({
      success: true,
      message,
      data: result?.data,
      pagination: result?.pagination,
      ...{(process.env.NODE_ENV === 'development') ? { requestId } : {}},
    });
  },
};